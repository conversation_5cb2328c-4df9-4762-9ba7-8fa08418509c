// MainActivity.kt
package com.alpha.demo

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.animation.AnimatedVisibilityScope
import androidx.compose.animation.EnterExitState
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionLayout
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.ScaleFactor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import kotlinx.coroutines.Dispatchers
import androidx.compose.ui.layout.ContentScale.Companion.Crop
import androidx.compose.ui.layout.ContentScale.Companion.Fit

// ---------- Activity ----------
class MainActivity : ComponentActivity() {
    @OptIn(ExperimentalSharedTransitionApi::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            MaterialTheme {
                SharedTransitionLayout {
                    val nav = rememberNavController()
                    AppNav(nav, sharedTransitionScope = this@SharedTransitionLayout)
                }
            }
        }
    }
}

// ---------- 导航 ----------
@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
fun AppNav(
    nav: NavHostController,
    sharedTransitionScope: SharedTransitionScope
) {
    NavHost(navController = nav, startDestination = "grid") {
        composable("grid") {
            GridScreen(
                sharedTransitionScope = sharedTransitionScope,
                animatedVisibilityScope = this@composable,
            ) { id -> nav.navigate("detail/$id") }
        }
        composable("detail/{id}") { backStackEntry ->
            val id = backStackEntry.arguments?.getString("id")!!
            DetailScreen(
                id = id,
                sharedTransitionScope = sharedTransitionScope,
                animatedVisibilityScope = this@composable
            ) { nav.popBackStack() }
        }
    }
}

// ---------- 假数据 ----------
data class Photo(val id: String, val url: String, val aspect: Float) // aspect = w/h

private val demoPhotos = listOf(
    Photo("1", "https://images.unsplash.com/photo-1519681393784-d120267933ba", 4096f / 2733f),
    Photo("2", "https://images.unsplash.com/photo-1513151233558-d860c5398176", 429f / 286f),
    Photo("3", "https://images.unsplash.com/photo-1500530855697-b586d89ba3ee", 3648f / 5472f),
)

// ---------- 网格页 ----------
@OptIn(ExperimentalSharedTransitionApi::class, ExperimentalMaterial3Api::class)
@Composable
fun GridScreen(
    sharedTransitionScope: SharedTransitionScope,
    animatedVisibilityScope: AnimatedVisibilityScope,
    onClick: (String) -> Unit
) {
    // 统一的共享过渡 spec（两端都用）
    val sharedSpec = remember { tween(durationMillis = 520, easing = FastOutSlowInEasing) }

    Scaffold(topBar = { TopAppBar(title = { Text("Gallery") }) }) { padding ->
        LazyVerticalGrid(
            columns = GridCells.Fixed(3),
            contentPadding = padding,
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp)
        ) {
            items(demoPhotos) { p ->
                PhotoSquareCell(
                    photo = p,
                    onClick = onClick,
                    sharedTransitionScope = sharedTransitionScope,
                    animatedVisibilityScope = animatedVisibilityScope,
                    sharedDurationSpec = sharedSpec
                )
            }
        }
    }
}

@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
private fun PhotoSquareCell(
    photo: Photo,
    onClick: (String) -> Unit,
    sharedTransitionScope: SharedTransitionScope,
    animatedVisibilityScope: AnimatedVisibilityScope,
    sharedDurationSpec: androidx.compose.animation.core.AnimationSpec<Float>
) {
    val painter = rememberImagePainterShared(photo.url)
    with(sharedTransitionScope) {
        Box(
            modifier = Modifier
                .sharedBounds(
                    rememberSharedContentState("card-${photo.id}"),
                    animatedVisibilityScope = animatedVisibilityScope,
                    boundsTransform = { _, _ -> sharedDurationSpec }
                )
                .aspectRatio(1f)
                .clickable { onClick(photo.id) }
        ) {
            Image(
                painter = painter,
                contentDescription = null,
                modifier = Modifier
                    .sharedElement(
                        rememberSharedContentState("photo-${photo.id}"),
                        animatedVisibilityScope = animatedVisibilityScope,
                        boundsTransform = { _, _ -> sharedDurationSpec }
                    )
                    .fillMaxSize(),
                contentScale = Crop
            )
        }
    }
}

// ---------- 详情页（裁剪可插值 + 统一 spec） ----------
@OptIn(ExperimentalSharedTransitionApi::class, ExperimentalMaterial3Api::class)
@Composable
fun DetailScreen(
    id: String,
    sharedTransitionScope: SharedTransitionScope,
    animatedVisibilityScope: AnimatedVisibilityScope,
    onBack: () -> Unit
) {
    val photo = remember(id) { demoPhotos.first { it.id == id } }
    val painter = rememberImagePainterShared(photo.url)

    // 同一份共享过渡 spec（与 Grid 端一致）
    val sharedSpec = remember { tween(durationMillis = 520, easing = FastOutSlowInEasing) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Photo #$id", maxLines = 1, overflow = TextOverflow.Ellipsis) },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { padding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding),
            contentAlignment = Alignment.TopCenter
        ) {
            // 统一时间轴进度：用于裁剪插值
            val transition = animatedVisibilityScope.transition
            val progress by transition.animateFloat(label = "cropProgress") { state ->
                if (state == EnterExitState.Visible) 1f else 0f
            }

            with(sharedTransitionScope) {
                Box(
                    modifier = Modifier
                        .sharedBounds(
                            rememberSharedContentState("card-${photo.id}"),
                            animatedVisibilityScope = animatedVisibilityScope,
                            boundsTransform = { _, _ -> sharedSpec }
                        )
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 12.dp)
                        .aspectRatio(photo.aspect)
                ) {
                    Image(
                        painter = painter,
                        contentDescription = null,
                        modifier = Modifier
                            .sharedElement(
                                rememberSharedContentState("photo-${photo.id}"),
                                animatedVisibilityScope = animatedVisibilityScope,
                                boundsTransform = { _, _ -> sharedSpec }
                            )
                            .fillMaxSize(),
                        contentScale = interpolatingContentScale(
                            from = Crop,
                            to = Fit,
                            progress = progress
                        )
                    )
                }
            }
        }
    }
}

// ---------- 可插值 ContentScale：在 0..1 间插值 ScaleFactor ----------
fun interpolatingContentScale(
    from: ContentScale,
    to: ContentScale,
    progress: Float
): ContentScale = object : ContentScale {
    override fun computeScaleFactor(
        srcSize: androidx.compose.ui.geometry.Size,
        dstSize: androidx.compose.ui.geometry.Size
    ): ScaleFactor {
        val a = from.computeScaleFactor(srcSize, dstSize)
        val b = to.computeScaleFactor(srcSize, dstSize)
        val t = progress.coerceIn(0f, 1f)
        val sx = a.scaleX + (b.scaleX - a.scaleX) * t
        val sy = a.scaleY + (b.scaleY - a.scaleY) * t
        return ScaleFactor(sx, sy)
    }
}

// ---------- Coil 工具：共享过渡内关闭 crossfade + 稳定缓存键 ----------
@Composable
private fun rememberImagePainterShared(url: String) = rememberAsyncImagePainter(
    ImageRequest.Builder(LocalContext.current)
        .data(url)
        .crossfade(false)                 // 关键：避免共享过渡尾部再淡入造成二次峰
        .memoryCacheKey("photo:$url")     // 稳定缓存键，保证两端命中同一资源
        .dispatcher(Dispatchers.IO)
        .build()
)
