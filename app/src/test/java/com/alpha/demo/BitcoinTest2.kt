package com.alpha.demo

import java.io.File
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

class BitcoinTest2 {
    // 历史价格记录
    data class PriceRecord(
        val date: LocalDate,
        val price: Double
    )

    // 回测交易结果
    data class TradeResult(
        val buyDate: LocalDate,
        val buyPrice: Double,
        val sellDate: LocalDate,
        val sellPrice: Double,
        val holdDays: Long,
        val returnRate: Double
    )

    // 主函数
    fun main() {
        // 假设文件名为 "btc.csv"
        val filePath = "/Users/<USER>/Downloads/Bitcoin Historical Data.csv"
//        val filePath = "/Users/<USER>/Downloads/Ethereum Historical Data.csv"

        // 1. 读取 CSV 并解析
        val dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy")
        val lines = File(filePath).readLines().drop(1) // 如果第一行是表头就 drop(1)

        // 解析每一行 -> PriceRecord
        val priceRecordsDesc = lines.mapNotNull { line ->
            val parts = line.split("\",\"")
            if (parts.size < 2) {
                null
            } else {
                // 第 1 列：日期；第 2 列：价格
                val date = LocalDate.parse(parts[0].replace("\"", "").trim(), dateFormatter)
                val price = parts[1].replace("\"", "").replace(",", "").trim().toDoubleOrNull() ?: 0.0
                PriceRecord(date, price)
            }
        }

        val priceRecords: List<PriceRecord> = priceRecordsDesc.reversed()
        val trades = mutableListOf<TradeResult>()


        // 历史高点的信息
        var lastHighPrice = Double.MIN_VALUE
        var lastHighDate: LocalDate? = null

        // 为规则1 维护的“一年之后可买入”日期（如果期间没出现新高）
        var rule1PotentialBuyDate: LocalDate? = null

        // 为规则2 维护：高点出现后 3 个月内是否出现下跌再回到高点
        var dippedFromHigh = false  // 标记是否从高点向下跌过

        // 当前持仓信息
        var holding = false
        var buyDate: LocalDate? = null
        var buyPrice = 0.0
        var buyHighPrice = 0.0  // 买入时对应的历史高点 A
        var sellDate: LocalDate? = null

        // 持有期间动态最高价（用于监控 20% 回撤）
        var peakPriceSinceBuy = 0.0

        // 逐日遍历
        for ((index, record) in priceRecords.withIndex()) {
            val currentDate = record.date
            val currentPrice = record.price

            // 0) 先检查是否出现了新的全局历史新高
            if (currentPrice > lastHighPrice) {
                // 出现了新的历史高点，重置
                lastHighPrice = currentPrice
                lastHighDate = currentDate

                // 出现新高，则要重置“一年之后可买入”的日期
                rule1PotentialBuyDate = lastHighDate?.plusYears(1)

                // 出现新高，也意味着 rule2 (3 个月内二次触及) 要重新记录
                dippedFromHigh = false
            }

            // 1) 如果当前无持仓，检查买入条件
            if (!holding) {
                // 1.1) 规则1：如果当前日期 >= rule1PotentialBuyDate，且我们没有在此期间出现新的高点
                //     那么可以尝试买入
                if (rule1PotentialBuyDate != null &&
                    !holding &&
                    !currentDate.isBefore(rule1PotentialBuyDate) // 等价于 currentDate >= rule1PotentialBuyDate
                ) {
                    println("买入规则1：$currentDate $currentPrice")
                    // 触发买入
                    holding = true
                    buyDate = currentDate
                    buyPrice = currentPrice
                    buyHighPrice = lastHighPrice // 当时的历史高点 A
                    peakPriceSinceBuy = currentPrice
                } else {
//                    println("检查规则2：$currentDate, dippedFromHigh = $dippedFromHigh, currentPrice = $currentPrice, lastHighPrice = $lastHighPrice")
                    // 1.2) 规则2：在 lastHighDate 之后 3 个月内，如果先出现了下跌(dippedFromHigh=true)，
                    //      又回到 lastHighPrice，且无持仓，则买入
                    //      条件： currentPrice == lastHighPrice
                    //           currentDate <= lastHighDate + 3个月
                    if (sellDate != null) {
                        val highDatePlus3 = sellDate!!.plusMonths(3)
                        if (currentDate <= highDatePlus3 &&
                            currentPrice >= lastHighPrice // “二次触及”或突破
                        ) {
                            println("买入规则2：$currentDate $currentPrice")
                            // 触发买入
                            holding = true
                            buyDate = currentDate
                            buyPrice = currentPrice
                            buyHighPrice = lastHighPrice
                            peakPriceSinceBuy = currentPrice
                        }
                    }
                }
            } else {
                // 2) 如果已经持仓，检查是否要更新“持有期动态最高价”
                if (currentPrice > peakPriceSinceBuy) {
                    peakPriceSinceBuy = currentPrice
                }
                // 当出现新的历史高点并且高于买入时的高点 A，则更新 buyHighPrice
                // 不过通常我们只要维护 peakPriceSinceBuy 即可，因为卖出看的是从买入后最高价回落20%
                // buyHighPrice 可以用来对比 B > A，但逻辑中只要 peakPriceSinceBuy 最高了就行
                // 如果要严格区分，可以如下写：
                if (currentPrice > buyHighPrice) {
                    buyHighPrice = currentPrice
                }

                // 2.1) 如果当前价格从 持有期最高价 peakPriceSinceBuy 回落 20%，则卖出
                if ((peakPriceSinceBuy == lastHighPrice) && (currentPrice <= peakPriceSinceBuy * 0.6)) {
//                    println("触发卖出：$currentDate, currentPrice = $currentPrice, lastHighPrice = $lastHighPrice, peakPriceSinceBuy = $peakPriceSinceBuy")
                    // 触发卖出
                    sellDate = currentDate
                    val sellPrice = currentPrice
                    val holdDays = ChronoUnit.DAYS.between(buyDate, sellDate)
                    val returnRate = (sellPrice - buyPrice) / buyPrice

                    trades.add(
                        TradeResult(
                            buyDate!!,
                            buyPrice,
                            sellDate,
                            sellPrice,
                            holdDays,
                            returnRate
                        )
                    )

                    // 平仓后，重置持仓状态
                    holding = false
                    buyDate = null
                    buyPrice = 0.0
                    buyHighPrice = 0.0
                    peakPriceSinceBuy = 0.0
                }
            }

            // 3) 规则2 辅助：如果当前是高点之后、价格确实比高点低，则说明已经下跌过
            //    这里需保证是在 lastHighDate 之后出现的价格
            if (lastHighDate != null && currentDate.isAfter(lastHighDate) && currentPrice < lastHighPrice) {
                // 标记已经出现过下跌
                dippedFromHigh = true
            }
        }


        // 4. 输出结果
        println("回测完成，共有 ${trades.size} 笔交易。")
        println("买入时间, 买入价格, 卖出时间, 卖出价格, 持有天数, 收益率(%), 比例")
        for (trade in trades) {
            println(
                "${trade.buyDate}, " +
                        "${"%.2f".format(trade.buyPrice)}, " +
                        "${trade.sellDate}, " +
                        "${"%.2f".format(trade.sellPrice)}, " +
                        "${trade.holdDays}, " +
                        "${"%.2f".format(trade.returnRate * 100)}, " +
                        "%.2f".format(trade.sellPrice / trade.buyPrice)
            )
        }
    }

}