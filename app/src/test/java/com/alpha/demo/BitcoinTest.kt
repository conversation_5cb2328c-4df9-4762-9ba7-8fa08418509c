package com.alpha.demo

import java.io.File
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import kotlin.math.abs

class BitcoinTest {
    // 历史价格记录
    data class PriceRecord(
        val date: LocalDate,
        val price: Double
    )


    data class OutputRecord(
        val date: LocalDate,
        val bitcoinPrice: Double,
        val isBuy: Boolean,
        val isSell: Boolean,
        val asset: Double
    )

    fun backtesting(priceRecords: List<PriceRecord>) {
        if (priceRecords.isEmpty()) return

        // 按日期排序
        val sortedRecords = priceRecords.sortedBy { it.date }

        // 归一化价格
        val initialPrice = sortedRecords.first().price
        val normalizedRecords = sortedRecords.map { PriceRecord(it.date, it.price / initialPrice) }

        val n = normalizedRecords.size
        if (n == 0) return

        // 预处理两年窗口的最大值数组
        val maxTwoYear = DoubleArray(n)
        val dequeMax = ArrayDeque<Int>()
        var leftMax = 0

        for (i in normalizedRecords.indices) {
            val currentDate = normalizedRecords[i].date
            val twoYearsAgo = currentDate.minusYears(2)

            while (leftMax < i && normalizedRecords[leftMax].date < twoYearsAgo) {
                leftMax++
            }

            while (dequeMax.isNotEmpty() && dequeMax.first() < leftMax) {
                dequeMax.removeFirst()
            }

            while (dequeMax.isNotEmpty() && normalizedRecords[dequeMax.last()].price <= normalizedRecords[i].price) {
                dequeMax.removeLast()
            }

            dequeMax.addLast(i)
            maxTwoYear[i] = normalizedRecords[dequeMax.first()].price
        }

        // 预处理一年窗口的最小值数组
        val minOneYear = DoubleArray(n)
        val dequeMin = ArrayDeque<Int>()
        var leftMin = 0

        for (i in normalizedRecords.indices) {
            val currentDate = normalizedRecords[i].date
            val oneYearAgo = currentDate.minusYears(1)

            while (leftMin < i && normalizedRecords[leftMin].date < oneYearAgo) {
                leftMin++
            }

            while (dequeMin.isNotEmpty() && dequeMin.first() < leftMin) {
                dequeMin.removeFirst()
            }

            while (dequeMin.isNotEmpty() && normalizedRecords[dequeMin.last()].price >= normalizedRecords[i].price) {
                dequeMin.removeLast()
            }

            dequeMin.addLast(i)
            minOneYear[i] = normalizedRecords[dequeMin.first()].price
        }

        // 确定交易开始日期
        val initialDate = sortedRecords.first().date
        val twoYearsLater = initialDate.plusYears(2)
        var startIndex = 0

        while (startIndex < normalizedRecords.size && normalizedRecords[startIndex].date < twoYearsLater) {
            startIndex++
        }

        if (startIndex >= normalizedRecords.size) return

        // 初始化状态
        var holding = false
        var cash = 1.0
        var bitcoinAmount = 0.0
        val outputRecords = mutableListOf<OutputRecord>()

        for (i in startIndex until normalizedRecords.size) {
            val currentRecord = normalizedRecords[i]
            val currentPrice = currentRecord.price
            val date = currentRecord.date
            var isBuy = false
            var isSell = false

            if (holding) {
                if (currentPrice >= maxTwoYear[i]) {
                    // 卖出
                    cash += bitcoinAmount * currentPrice
                    bitcoinAmount = 0.0
                    holding = false
                    isSell = true
                }
            } else {
                if (currentPrice <= minOneYear[i]) {
                    // 买入
                    if (cash > 0) {
                        bitcoinAmount = cash / currentPrice
                        cash = 0.0
                        holding = true
                        isBuy = true
                    }
                }
            }

            val asset = if (holding) bitcoinAmount * currentPrice else cash
            outputRecords.add(OutputRecord(date, currentPrice, isBuy, isSell, asset))
        }

        // 写入CSV文件
        File("output.csv").printWriter().use { writer ->
            writer.println("日期,比特币价格（初始为1）,是否买入（true/false）,是否卖出（true/false）,资产（初始为1）")
            outputRecords.forEach { record ->
                val dateStr = record.date.toString()
                val priceStr = "%.6f".format(record.bitcoinPrice)
                val buyStr = if (record.isBuy) "true" else "false"
                val sellStr = if (record.isSell) "true" else "false"
                val assetStr = "%.6f".format(record.asset)
                writer.println("$dateStr,$priceStr,$buyStr,$sellStr,$assetStr")
            }
        }
    }





    // 主函数
    fun main() {
        val filePath = "/Users/<USER>/Downloads/Bitcoin Historical Data.csv"
//        val filePath = "/Users/<USER>/Downloads/Ethereum Historical Data.csv"

        // 读取 CSV 并解析
        val dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy")
        val lines = File(filePath).readLines().drop(1) // 如果第一行是表头就 drop(1)

        // 解析每一行 -> PriceRecord
        val priceRecordsDesc = lines.mapNotNull { line ->
            val parts = line.split("\",\"")
            if (parts.size < 2) {
                null
            } else {
                // 第 1 列：日期；第 2 列：价格
                val date = LocalDate.parse(parts[0].replace("\"", "").trim(), dateFormatter)
                val price = parts[1].replace("\"", "").replace(",", "").trim().toDoubleOrNull() ?: 0.0
                PriceRecord(date, price)
            }
        }

        val priceRecords: List<PriceRecord> = priceRecordsDesc.reversed()
        backtesting(priceRecords)

//        // 输出结果
//        println("回测完成，共有 ${trades.size} 笔交易。")
//        println("买入时间, 买入价格, 卖出时间, 卖出价格, 持有天数, 收益率(%), 比例, 累计")
//        var current = 1.0
//        for (trade in trades) {
//            val factor = trade.sellPrice / trade.buyPrice
//            current *= factor
//            println(
//                "${trade.buyDate}, " +
//                        "${"%.2f".format(trade.buyPrice)}, " +
//                        "${trade.sellDate}, " +
//                        "${"%.2f".format(trade.sellPrice)}, " +
//                        "${trade.holdDays}, " +
//                        "${"%.2f".format(trade.returnRate * 100)}, " +
//                        "${"%.2f".format(factor)}, " +
//                        "${"%.2f".format(current)} "
//            )
//        }
    }

}